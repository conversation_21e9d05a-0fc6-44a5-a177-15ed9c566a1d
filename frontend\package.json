{"name": "vite-react-javascript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.2", "@tailwindcss/vite": "^4.1.10", "axios": "^1.10.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}