# 🔍 Google Search Query Logging Added

## ✅ Enhancement Applied

Added detailed logging to show exactly what search query Selenium sends to Google when processing user prompts.

## 📍 Location of Changes

**File**: `backend/src/services/providers/scraper.js`  
**Function**: `scrapeLeadsWithSelenium()`  
**Line**: Around line 505-518 (before `driver.get(searchUrl)`)

## 🔧 Code Changes

### BEFORE:
```javascript
try {
  const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(`${category} ${location} contact phone number`)}`;
  console.log(`🔍 Searching: ${searchUrl}`);
  await driver.get(searchUrl);
```

### AFTER:
```javascript
try {
  // Log the original prompt and parsed keywords
  console.log(`📥 Prompt received: ${prompt}`);
  console.log(`🔍 Parsed keywords: ${category} in ${location}`);
  
  // Build the search query
  const searchQuery = `${category} ${location} contact phone number`;
  const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
  
  // Log the final search URL before navigation
  console.log(`🌐 Final search URL: ${searchUrl}`);
  console.log(`🔍 Google search query: ${searchUrl}`);
  
  await driver.get(searchUrl);
```

## 📋 Expected Log Output

When a user enters: **"generate 10 lead of restaurants in california"**

The console will now show:

```
🔍 SCRAPER STARTED - Prompt: "generate 10 lead of restaurants in california"
🔢 Extracted lead count: 10 from prompt: "generate 10 lead of restaurants in california"
🎯 Target: 10 restaurants in california
🚀 Setting up Chrome options...
🔍 Session key: restaurants_california
🤖 Headless mode: ALWAYS ENABLED (CAPTCHA handled in frontend)
✅ Using headless mode - CAPTCHA will be handled in React frontend
🚀 Building Chrome driver...
✅ Chrome driver created successfully
📥 Prompt received: generate 10 lead of restaurants in california
🔍 Parsed keywords: restaurants in california
🌐 Final search URL: https://www.google.com/search?q=restaurants%20california%20contact%20phone%20number
🔍 Google search query: https://www.google.com/search?q=restaurants%20california%20contact%20phone%20number
🛑 Checking for CAPTCHA...
```

## 🎯 Key Information Logged

### 1. **Original User Input**
```
📥 Prompt received: generate 10 lead of restaurants in california
```

### 2. **Parsed Keywords**
```
🔍 Parsed keywords: restaurants in california
```

### 3. **Final Google Search URL**
```
🌐 Final search URL: https://www.google.com/search?q=restaurants%20california%20contact%20phone%20number
🔍 Google search query: https://www.google.com/search?q=restaurants%20california%20contact%20phone%20number
```

## 🔍 Search Query Construction

The system automatically appends `contact phone number` to the user's category and location:

- **User Input**: "restaurants in california"
- **Search Query**: "restaurants california contact phone number"
- **URL Encoded**: `restaurants%20california%20contact%20phone%20number`

## 🧪 Testing Examples

### Example 1:
**Input**: `"generate 15 leads of dentists in New York"`
**Expected Logs**:
```
📥 Prompt received: generate 15 leads of dentists in New York
🔍 Parsed keywords: dentists in New York
🌐 Final search URL: https://www.google.com/search?q=dentists%20New%20York%20contact%20phone%20number
```

### Example 2:
**Input**: `"generate 5 software companies in San Francisco"`
**Expected Logs**:
```
📥 Prompt received: generate 5 software companies in San Francisco
🔍 Parsed keywords: software companies in San Francisco
🌐 Final search URL: https://www.google.com/search?q=software%20companies%20San%20Francisco%20contact%20phone%20number
```

### Example 3:
**Input**: `"find 20 plumbers in Chicago"`
**Expected Logs**:
```
📥 Prompt received: find 20 plumbers in Chicago
🔍 Parsed keywords: plumbers in Chicago
🌐 Final search URL: https://www.google.com/search?q=plumbers%20Chicago%20contact%20phone%20number
```

## 🛠️ Debugging Benefits

### 1. **Transparency**
- See exactly what search query is sent to Google
- Verify that prompt parsing is working correctly
- Understand how keywords are extracted and used

### 2. **Troubleshooting**
- Identify if search queries are too broad or too narrow
- Debug parsing issues with complex prompts
- Verify URL encoding is working properly

### 3. **Optimization**
- Analyze which search queries produce better results
- Fine-tune keyword extraction logic if needed
- Monitor search query patterns

## ✅ No Logic Changes

**Important**: This enhancement only adds logging for debugging purposes. No scraping logic, search behavior, or functionality has been modified. The system works exactly the same way but now provides detailed visibility into the search query construction process.

## 🚀 Ready for Testing

The enhanced logging is now active. When you run the scraper with any prompt, you'll see detailed information about:
- The original user prompt
- How keywords are parsed
- The exact Google search URL being used

This will help with debugging and understanding exactly how user prompts are converted into Google search queries! 🔍
