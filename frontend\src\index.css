@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar for email section - FULL PAGE RIGHT SIDE */
.custom-scrollbar {
  /* Remove container scrollbar */
  overflow-y: visible;
}

/* Apply scrollbar to the entire page/body when email section is active */
body.email-section-active {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  overflow-y: auto;
}

/* Webkit browsers (Chrome, Safari, Edge) - Full page scrollbar */
body.email-section-active::-webkit-scrollbar {
  width: 12px;
}

body.email-section-active::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

body.email-section-active::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
}

body.email-section-active::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
