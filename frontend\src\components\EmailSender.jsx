import React, { useState, useEffect } from 'react';
import axios from 'axios';
import * as XLSX from 'xlsx';

const API_BASE = import.meta.env.VITE_API_SERVER_URL || 'http://localhost:3001';

function EmailSection({ scrapedLeads = [], goBack }) {
  const [method, setMethod] = useState('generated');
  const [leads, setLeads] = useState([]);
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [fromEmail, setFromEmail] = useState('');
  const [attachment, setAttachment] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [spreadsheetId, setSpreadsheetId] = useState('');
  const [range, setRange] = useState('Sheet1!A:B');
  const [sheets, setSheets] = useState([]);
  const [loadingSheets, setLoadingSheets] = useState(false);
  const [authData, setAuthData] = useState(() => {
    // Try to load from localStorage for session continuity
    const stored = localStorage.getItem('googleAuthData');
    return stored ? JSON.parse(stored) : null;
  });
  const [sendingEmails, setSendingEmails] = useState(false);

  // Handle Google Auth success
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const authDataParam = params.get('authData');
    const returnToEmail = sessionStorage.getItem('returnToEmailSection');

    if (authDataParam && returnToEmail) {
      try {
        const parsed = JSON.parse(decodeURIComponent(authDataParam));
        setAuthData(parsed);
        localStorage.setItem('googleAuthData', JSON.stringify(parsed));
        sessionStorage.removeItem('returnToEmailSection');
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (e) {
        console.error('Failed to parse auth data:', e);
      }
    }
  }, []);

  // Handles both CSV and XLSX file uploads
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (fileExtension === 'csv') {
      import('papaparse').then(Papa => {
        Papa.parse(file, {
          header: true,
          complete: (results) => {
            setLeads(results.data);
          }
        });
      });
    } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
      const reader = new FileReader();
      reader.onload = (event) => {
        const data = new Uint8Array(event.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const parsedData = XLSX.utils.sheet_to_json(firstSheet);
        setLeads(parsedData);
      };
      reader.readAsArrayBuffer(file);
    } else {
      alert('Unsupported file type. Please upload a CSV or Excel file.');
    }
  };

  const handleAttachmentUpload = async (e) => {
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);

    const res = await axios.post('http://localhost:3001/api/email/upload', formData);
    setUploadedFile(res.data);
  };

  // Handle Google Auth
  const handleGoogleAuth = () => {
    // Store current state to return to email section
    sessionStorage.setItem('returnToEmailSection', 'true');
    // Redirect to Google Auth
    window.location.href = `${API_BASE}/api/auth/google`;
  };

  // Fetch user's Google Sheets list
  const fetchUserSheets = async () => {
    setLoadingSheets(true);
    try {
      if (!authData?.access_token) {
        handleGoogleAuth();
        return;
      }

      const res = await axios.post(`${API_BASE}/api/export/google-sheets/list`, {
        token: authData.access_token
      });

      if (res.data.success) {
        setSheets(res.data.spreadsheets || []);
      } else {
        throw new Error(res.data.error || 'Failed to fetch sheets');
      }
    } catch (err) {
      console.error('Google Sheets fetch error:', err);
      alert('Failed to fetch Google Sheets. Please try logging in again.');
      // Clear invalid auth data
      localStorage.removeItem('googleAuthData');
      setAuthData(null);
    }
    setLoadingSheets(false);
  };

  const fetchGoogleSheetLeads = async () => {
    if (!spreadsheetId) {
      alert('Please select a Google Sheet first');
      return;
    }

    setLoadingSheets(true);
    try {
      if (!authData?.access_token) {
        handleGoogleAuth();
        return;
      }

      const res = await axios.post(`${API_BASE}/api/export/google-sheets/fetch`, {
        token: authData.access_token,
        spreadsheetId,
        range
      });

      if (res.data.success) {
        setLeads(res.data.leads || []);
        alert(`✅ Successfully loaded ${res.data.leads?.length || 0} leads from Google Sheet!`);
      } else {
        throw new Error(res.data.error || 'Failed to fetch leads');
      }
    } catch (err) {
      console.error('Google Sheet leads fetch error:', err);
      alert('Failed to fetch leads from Google Sheet');
    }
    setLoadingSheets(false);
  };

  const sendEmails = async () => {
    const activeLeads = method === 'generated' ? scrapedLeads : leads;

    // Validation
    if (!fromEmail) {
      alert('Please enter sender email');
      return;
    }
    if (!subject) {
      alert('Please enter email subject');
      return;
    }
    if (!description) {
      alert('Please enter email description');
      return;
    }
    if (!activeLeads || activeLeads.length === 0) {
      alert('No leads available to send emails to');
      return;
    }

    setSendingEmails(true);

    try {
      const payload = {
        from: fromEmail,
        leads: activeLeads,
        subject,
        description,
        attachments: uploadedFile ? [uploadedFile] : []
      };

      console.log(`📧 Sending emails to ${activeLeads.length} leads:`, activeLeads);

      await axios.post(`${API_BASE}/api/email/send-emails`, payload);

      alert(`✅ Emails sent successfully to ${activeLeads.length} leads!`);
    } catch (error) {
      console.error('Email sending error:', error);
      alert('❌ Failed to send emails. Please try again.');
    } finally {
      setSendingEmails(false);
    }
  };

  return (
    <div className="flex-1 flex flex-col w-full">
      {/* Top Banner - exactly like the leads section, but with a Leads button */}
      <div className="flex justify-end items-center px-6 py-4 bg-gradient-to-r from-slate-900 to-blue-900 relative z-20">
        <button
          className="px-6 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-semibold"
          onClick={goBack}
        >
          Leads
        </button>
      </div>

      {/* Main Email Sender UI */}
      <div className="p-4 space-y-4 w-full max-w-2xl mx-auto">
        <h2 className="text-xl font-bold">Lead Email Sender</h2>

        <div className="space-x-4">
          <button className={`p-2 ${method === 'generated' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('generated')}>Use Scraped Leads</button>
          <button className={`p-2 ${method === 'local' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('local')}>Upload Local File</button>
          <button className={`p-2 ${method === 'google' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('google')}>Google Sheet</button>
        </div>

        {method === 'local' && (
          <div>
            <h4>Upload CSV or Excel File</h4>
            <input type="file" accept=".csv, .xls, .xlsx" onChange={handleFileUpload} />
          </div>
        )}

        {method === 'google' && (
          <div className="space-y-2">
            <button
              className="bg-blue-500 text-white p-2"
              onClick={fetchUserSheets}
              disabled={loadingSheets}
            >
              {loadingSheets ? 'Loading Sheets...' : 'Pick from My Google Sheets'}
            </button>
            {sheets.length > 0 && (
              <select
                className="border p-1 w-full"
                value={spreadsheetId}
                onChange={e => setSpreadsheetId(e.target.value)}
              >
                <option value="">Select a sheet</option>
                {sheets.map(sheet => (
                  <option key={sheet.id} value={sheet.id}>
                    {sheet.name}
                  </option>
                ))}
              </select>
            )}
            <input
              className="border p-1 w-full"
              placeholder="Range (e.g. Sheet1!A:B)"
              value={range}
              onChange={e => setRange(e.target.value)}
            />
            <button className="bg-green-500 text-white p-2" onClick={fetchGoogleSheetLeads} disabled={!spreadsheetId}>
              Fetch Leads
            </button>
          </div>
        )}

        <div className="space-y-4">
          <div className="space-y-2">
            <input
              type="email"
              className="border p-1 w-full"
              placeholder="Sender Email"
              value={fromEmail}
              onChange={(e) => setFromEmail(e.target.value)}
            />
            <input className="border p-1 w-full" placeholder="Subject" value={subject} onChange={e => setSubject(e.target.value)} />
            <textarea className="border p-1 w-full h-32" placeholder="Description" value={description} onChange={e => setDescription(e.target.value)} />
            <div className="flex items-center space-x-2">
              <span>Attachment:</span>
              <input type="file" onChange={handleAttachmentUpload} />
              {uploadedFile && <span className="text-green-600 text-sm">✓ {uploadedFile.name}</span>}
            </div>
          </div>

          {/* Display leads that will receive emails */}
          <div className="border p-4 rounded-lg bg-gray-50">
            <h3 className="font-semibold mb-2">Recipients ({method === 'generated' ? scrapedLeads.length : leads.length})</h3>
            <div className="max-h-40 overflow-y-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="text-left p-1">Name</th>
                    <th className="text-left p-1">Email</th>
                  </tr>
                </thead>
                <tbody>
                  {(method === 'generated' ? scrapedLeads : leads).map((lead, idx) => (
                    <tr key={idx} className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="p-1">{lead.name || lead['First Name'] || ''} {lead['Last Name'] || ''}</td>
                      <td className="p-1">{lead.email || lead['Email Address'] || lead.Email || ''}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <button
            className="bg-blue-600 hover:bg-blue-700 text-white p-2 w-full rounded transition-colors duration-200 font-medium"
            onClick={sendEmails}
            disabled={sendingEmails}
          >
            {sendingEmails ? 'Sending...' : 'Send Emails'}
          </button>
        </div>
      </div>
    </div>
  );
}

export default EmailSection;
