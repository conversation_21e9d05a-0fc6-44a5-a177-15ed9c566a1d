import React, { useState } from 'react';
import axios from 'axios';
import * as XLSX from 'xlsx';

function EmailSection({ scrapedLeads = [], goBack }) {
  const [method, setMethod] = useState('generated');
  const [leads, setLeads] = useState([]);
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [fromEmail, setFromEmail] = useState('');
  const [attachment, setAttachment] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [spreadsheetId, setSpreadsheetId] = useState('');
  const [range, setRange] = useState('Sheet1!A:B');
  const [sheets, setSheets] = useState([]);
  const [loadingSheets, setLoadingSheets] = useState(false);

  // Handles both CSV and XLSX file uploads
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (fileExtension === 'csv') {
      import('papaparse').then(Papa => {
        Papa.parse(file, {
          header: true,
          complete: (results) => {
            setLeads(results.data);
          }
        });
      });
    } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
      const reader = new FileReader();
      reader.onload = (event) => {
        const data = new Uint8Array(event.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const parsedData = XLSX.utils.sheet_to_json(firstSheet);
        setLeads(parsedData);
      };
      reader.readAsArrayBuffer(file);
    } else {
      alert('Unsupported file type. Please upload a CSV or Excel file.');
    }
  };

  const handleAttachmentUpload = async (e) => {
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);

    const res = await axios.post('http://localhost:3001/api/email/upload', formData);
    setUploadedFile(res.data);
  };

  // Fetch user's Google Sheets list
  const fetchUserSheets = async () => {
    setLoadingSheets(true);
    try {
      if (!authData?.access_token) {
        // If not authenticated, trigger login (reuse logic from GoogleSheetPicker if needed)
        window.location.href = `${API_BASE}/api/auth/google`;
        return;
      }
      const res = await axios.post(`${API_BASE}/api/export/google-sheets/list`, { token: authData.access_token });
      setSheets(res.data.spreadsheets || []);
    } catch (err) {
      alert('Failed to fetch Google Sheets');
    }
    setLoadingSheets(false);
  };

  const fetchGoogleSheetLeads = async () => {
    const res = await axios.post('http://localhost:3001/api/sheets/fetch', { spreadsheetId, range });
    setLeads(res.data.leads);
  };

  const sendEmails = async () => {
    const activeLeads = method === 'generated' ? scrapedLeads : leads;

    const payload = {
      from: fromEmail,
      leads: activeLeads,
      subject,
      description,
      attachments: uploadedFile ? [uploadedFile] : []
    };

    await axios.post('http://localhost:3001/api/email/send-emails', payload);
    alert('✅ Emails sent successfully!');
  };

  return (
    <div className="flex-1 flex flex-col w-full">
      {/* Top Banner - exactly like the leads section, but with a Leads button */}
      <div className="flex justify-end items-center px-6 py-4 bg-gradient-to-r from-slate-900 to-blue-900 relative z-20">
        <button
          className="px-6 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-semibold"
          onClick={goBack}
        >
          Leads
        </button>
      </div>

      {/* Main Email Sender UI */}
      <div className="p-4 space-y-4 w-full max-w-2xl mx-auto">
        <h2 className="text-xl font-bold">Lead Email Sender</h2>

        <div className="space-x-4">
          <button className={`p-2 ${method === 'generated' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('generated')}>Use Scraped Leads</button>
          <button className={`p-2 ${method === 'local' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('local')}>Upload Local File</button>
          <button className={`p-2 ${method === 'google' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`} onClick={() => setMethod('google')}>Google Sheet</button>
        </div>

        {method === 'local' && (
          <div>
            <h4>Upload CSV or Excel File</h4>
            <input type="file" accept=".csv, .xls, .xlsx" onChange={handleFileUpload} />
          </div>
        )}

        {method === 'google' && (
          <div className="space-y-2">
            <button
              className="bg-blue-500 text-white p-2"
              onClick={fetchUserSheets}
              disabled={loadingSheets}
            >
              {loadingSheets ? 'Loading Sheets...' : 'Pick from My Google Sheets'}
            </button>
            {sheets.length > 0 && (
              <select
                className="border p-1 w-full"
                value={spreadsheetId}
                onChange={e => setSpreadsheetId(e.target.value)}
              >
                <option value="">Select a sheet</option>
                {sheets.map(sheet => (
                  <option key={sheet.id} value={sheet.id}>
                    {sheet.name}
                  </option>
                ))}
              </select>
            )}
            <input
              className="border p-1 w-full"
              placeholder="Range (e.g. Sheet1!A:B)"
              value={range}
              onChange={e => setRange(e.target.value)}
            />
            <button className="bg-green-500 text-white p-2" onClick={fetchGoogleSheetLeads} disabled={!spreadsheetId}>
              Fetch Leads
            </button>
          </div>
        )}

        <div className="space-y-2">
          <input
            type="email"
            className="border p-1 w-full"
            placeholder="Sender Email"
            value={fromEmail}
            onChange={(e) => setFromEmail(e.target.value)}
          />
          <input className="border p-1 w-full" placeholder="Subject" value={subject} onChange={e => setSubject(e.target.value)} />
          <textarea className="border p-1 w-full" placeholder="Description" value={description} onChange={e => setDescription(e.target.value)} />
          <input type="file" onChange={handleAttachmentUpload} />
          <button className="bg-blue-600 text-white p-2" onClick={sendEmails}>Send Emails</button>
        </div>
      </div>
    </div>
  );
}

export default EmailSection;
