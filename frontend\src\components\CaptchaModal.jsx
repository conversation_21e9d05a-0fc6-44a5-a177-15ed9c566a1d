import { useState, useEffect, useRef } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';

const CaptchaModal = ({ isOpen, onClose, onSolved, siteKey, sessionId, prompt }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeLeft, setTimeLeft] = useState(180); // 3 minutes in seconds
  const [captchaSolved, setCaptchaSolved] = useState(false);
  const [captchaExpired, setCaptchaExpired] = useState(false);
  const recaptchaRef = useRef(null);

  // Countdown timer effect
  useEffect(() => {
    if (!isOpen) return;

    // Reset states when modal opens
    setTimeLeft(180);
    setCaptchaSolved(false);
    setCaptchaExpired(false);
    setError('');

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCaptchaExpired(true);
          setError('❌ CAPTCHA expired. Please try again.');
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCaptchaSuccess = async (token) => {
    console.log('🎯 CAPTCHA solved successfully!', token);
    setCaptchaSolved(true);
    setIsLoading(true);
    setError('');

    try {
      // Send the token to backend
      const response = await fetch(`${import.meta.env.VITE_API_SERVER_URL}/api/captcha/solve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token,
          sessionId: sessionId,
          prompt: prompt
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ CAPTCHA token sent to backend successfully');
        setError('✅ CAPTCHA solved, scraping will now resume');

        // Wait a moment to show success message
        setTimeout(() => {
          onSolved(token);
          onClose();
        }, 1500);
      } else {
        throw new Error(result.error || 'Failed to process CAPTCHA');
      }
    } catch (error) {
      console.error('❌ Error sending CAPTCHA token:', error);
      setCaptchaSolved(false);
      setError(`Failed to process CAPTCHA: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaptchaError = () => {
    console.error('❌ reCAPTCHA error occurred');
    setCaptchaSolved(false);
    setError('⚠️ CAPTCHA closed prematurely, please retry.');
  };

  const handleCaptchaExpired = () => {
    console.log('⏰ reCAPTCHA expired');
    setCaptchaSolved(false);
    setCaptchaExpired(true);
    setError('❌ CAPTCHA expired or failed. Please try again.');
  };

  const handleRetry = () => {
    console.log('🔄 Retrying CAPTCHA...');
    setError('');
    setCaptchaSolved(false);
    setCaptchaExpired(false);
    setTimeLeft(180);

    // Reset the reCAPTCHA component
    if (recaptchaRef.current) {
      recaptchaRef.current.reset();
    }

    // Close modal and trigger retry
    onClose();
    // The parent component should handle restarting the scrape
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">CAPTCHA Verification Required</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
            disabled={isLoading}
          >
            ×
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 text-sm mb-2">
            🔐 Please complete the CAPTCHA verification to continue scraping leads.
          </p>
          {prompt && (
            <p className="text-sm text-blue-600 mt-2 font-medium">
              Searching for: "{prompt}"
            </p>
          )}

          {/* Countdown Timer */}
          <div className="mt-4 p-3 bg-gray-100 rounded-lg">
            <div className="flex items-center justify-center space-x-2">
              <span className="text-lg font-mono font-bold text-gray-800">
                ⏱️ {formatTime(timeLeft)}
              </span>
              <span className="text-sm text-gray-600">remaining</span>
            </div>
            {timeLeft <= 30 && timeLeft > 0 && (
              <p className="text-red-600 text-sm mt-1 font-medium">
                ⚠️ Time running out!
              </p>
            )}
            {captchaSolved && (
              <p className="text-green-600 text-sm mt-1 font-medium">
                ✅ CAPTCHA solved successfully!
              </p>
            )}
          </div>

          <p className="text-xs text-gray-500 mt-2">
            Session ID: {sessionId}
          </p>
        </div>

        {error && (
          <div className={`mb-4 p-3 rounded ${
            error.includes('✅')
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            {error}
          </div>
        )}

        <div className="flex justify-center mb-4">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Processing CAPTCHA...</span>
            </div>
          ) : captchaExpired || timeLeft === 0 ? (
            <div className="text-center p-8">
              <p className="text-red-600 mb-4">❌ CAPTCHA session expired</p>
              <button
                onClick={handleRetry}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                🔄 Retry
              </button>
            </div>
          ) : (
            <div className="flex justify-center">
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY || siteKey}
                onChange={handleCaptchaSuccess}
                onError={handleCaptchaError}
                onExpired={handleCaptchaExpired}
                theme="light"
                size="normal"
              />
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            After solving the CAPTCHA, the scraping process will automatically resume.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CaptchaModal;
