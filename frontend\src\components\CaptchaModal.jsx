import React, { useState, useEffect, useRef } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';

const CaptchaModal = ({ isOpen, onClose, onSolved, siteKey, sessionId, prompt }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [effectiveSiteKey, setEffectiveSiteKey] = useState('');
  const [domainVerified, setDomainVerified] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const recaptchaRef = useRef(null);

  // 🔐 ENHANCED DOMAIN VERIFICATION AND SITE KEY SETUP
  useEffect(() => {
    if (!isOpen) {
      // Reset states when modal closes
      setError('');
      setRetryCount(0);
      setIsLoading(false);
      return;
    }

    const currentDomain = window.location.hostname;
    const envSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

    console.log('🔐 reCAPTCHA Domain Verification:', {
      currentDomain,
      envSiteKey: envSiteKey ? `${envSiteKey.substring(0, 20)}...` : 'Missing',
      backendSiteKey: siteKey ? `${siteKey.substring(0, 20)}...` : 'Missing',
      isProduction: currentDomain.includes('vercel.app'),
      isDevelopment: currentDomain === 'localhost'
    });

    // 🎯 PRIORITY: Environment variable first (for production), then backend fallback
    const finalSiteKey = envSiteKey || siteKey;

    if (finalSiteKey) {
      // Validate site key format
      if (finalSiteKey.length < 30 || !finalSiteKey.startsWith('6L')) {
        setError('Invalid reCAPTCHA site key format. Please check configuration.');
        console.error('❌ Invalid site key format:', finalSiteKey);
        return;
      }

      setEffectiveSiteKey(finalSiteKey);
      setDomainVerified(true);
      setError(''); // Clear any previous errors

      console.log('✅ reCAPTCHA Configuration Valid:', {
        siteKey: finalSiteKey.substring(0, 20) + '...',
        domain: currentDomain,
        source: envSiteKey ? 'Environment Variable' : 'Backend'
      });
    } else {
      const errorMsg = 'reCAPTCHA site key not configured. Please contact support.';
      setError(errorMsg);
      console.error('❌ No reCAPTCHA site key available');
    }
  }, [isOpen, siteKey, retryCount]);

  const handleCaptchaSuccess = async (token) => {
    console.log('🎯 CAPTCHA solved successfully!', token);
    setIsLoading(true);
    setError('');

    try {
      // Send the token to backend
      const response = await fetch(`${import.meta.env.VITE_API_SERVER_URL}/api/captcha/solve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token,
          sessionId: sessionId,
          prompt: prompt
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ CAPTCHA token sent to backend successfully');
        onSolved(token);
        onClose();
      } else {
        throw new Error(result.error || 'Failed to process CAPTCHA');
      }
    } catch (error) {
      console.error('❌ Error sending CAPTCHA token:', error);
      setError(`Failed to process CAPTCHA: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaptchaError = (error) => {
    console.error('❌ reCAPTCHA error occurred:', error);
    setRetryCount(prev => prev + 1);

    if (retryCount < 2) {
      setError(`CAPTCHA error occurred. Retrying... (${retryCount + 1}/3)`);
      // Auto-retry by resetting the component
      setTimeout(() => {
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
        }
      }, 2000);
    } else {
      setError('CAPTCHA failed multiple times. Please refresh the page and try again.');
    }
  };

  const handleCaptchaExpired = () => {
    console.log('⏰ reCAPTCHA expired');
    setError('CAPTCHA expired. Please solve it again.');
    if (recaptchaRef.current) {
      recaptchaRef.current.reset();
    }
  };

  const handleRetry = () => {
    setError('');
    setRetryCount(0);
    setIsLoading(false);
    if (recaptchaRef.current) {
      recaptchaRef.current.reset();
    }
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">CAPTCHA Verification Required</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
            disabled={isLoading}
          >
            ×
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 text-sm mb-2">
            Please complete the CAPTCHA verification to continue scraping leads.
          </p>
          <p className="text-xs text-gray-500">
            Session ID: {sessionId}
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="flex justify-center mb-4">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Processing CAPTCHA...</span>
            </div>
          ) : domainVerified && effectiveSiteKey ? (
            <div className="flex flex-col items-center">
              <div className="mb-2">
                <ReCAPTCHA
                  ref={recaptchaRef}
                  sitekey={effectiveSiteKey}
                  onChange={handleCaptchaSuccess}
                  onError={handleCaptchaError}
                  onExpired={handleCaptchaExpired}
                  theme="light"
                  size="normal"
                />
              </div>
              {retryCount > 0 && (
                <button
                  onClick={handleRetry}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  Reset CAPTCHA
                </button>
              )}
            </div>
          ) : (
            <div className="text-center p-4">
              <p className="text-red-600 mb-2">reCAPTCHA Configuration Error</p>
              <p className="text-sm text-gray-600 mb-3">
                Domain: {window.location.hostname}<br/>
                Site Key: {effectiveSiteKey ? 'Present' : 'Missing'}<br/>
                Please verify the domain is registered with reCAPTCHA.
              </p>
              <button
                onClick={handleRetry}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Retry Configuration
              </button>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            After solving the CAPTCHA, the scraping process will automatically resume.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CaptchaModal;
