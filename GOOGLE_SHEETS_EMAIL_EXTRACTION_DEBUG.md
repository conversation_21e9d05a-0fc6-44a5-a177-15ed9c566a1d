# 🔍 **Google Sheets Email Extraction Debug Guide**

## 🚨 **Issue Identified:**
From the screenshot, Google Sheets is fetching **Name: "<PERSON><PERSON><PERSON>"** but **Email: (empty)**. This means the email column is not being properly extracted.

## 🔧 **Enhanced Debugging Added:**

### **Backend Debug** (`/api/export/google-sheets/fetch`):
```javascript
console.log('📊 Google Sheets fetch debug:');
console.log('   📋 Raw response:', response.data.values);
console.log('   📝 Headers found:', header);
console.log('   📊 Data rows count:', rows.length);
console.log('   📋 Sample row:', rows[0]);
console.log('   🔍 Sample lead object:', lead);
console.log('   ✅ Total leads processed:', leads.length);
```

### **Frontend Debug** (Already in place):
```javascript
console.log('📊 Fetched leads from Google Sheet:', fetchedLeads);
console.log('📋 Sample lead structure:', fetchedLeads[0]);
console.log('📧 Available keys:', Object.keys(fetchedLeads[0]));
```

---

## 🧪 **Step-by-Step Debug Process:**

### **Step 1: Check Your Google Sheet Structure**
Your Google Sheet should look like this:
```
| Name        | Email                |
|-------------|---------------------|
| Bilal Ahmad | <EMAIL>   |
| John Doe    | <EMAIL>    |
```

**❌ Common Issues:**
- Missing email column
- Email column has different name (e.g., "E-mail", "Email Address")
- Empty email cells
- Wrong range selected (e.g., only selecting Name column)

### **Step 2: Test with Debug Logs**
1. Go to **Email section**
2. Click **Google Sheet** button
3. Select your sheet and set range (e.g., `Sheet1!A:B`)
4. Click **Fetch Leads**
5. **Check browser console** (F12 → Console tab)
6. **Check backend terminal** for debug logs

### **Step 3: Analyze Debug Output**

#### **Expected Backend Logs:**
```
📊 Google Sheets fetch debug:
   📋 Raw response: [["Name", "Email"], ["Bilal Ahmad", "<EMAIL>"]]
   📝 Headers found: ["Name", "Email"]
   📊 Data rows count: 1
   📋 Sample row: ["Bilal Ahmad", "<EMAIL>"]
   🔍 Sample lead object: {Name: "Bilal Ahmad", Email: "<EMAIL>"}
   ✅ Total leads processed: 1
```

#### **Expected Frontend Logs:**
```
📊 Fetched leads from Google Sheet: [{Name: "Bilal Ahmad", Email: "<EMAIL>"}]
📋 Sample lead structure: {Name: "Bilal Ahmad", Email: "<EMAIL>"}
📧 Available keys: ["Name", "Email"]
```

---

## 🔧 **Common Fixes:**

### **Fix 1: Check Google Sheet Range**
If you're only seeing names, you might be selecting wrong range:

**❌ Wrong**: `Sheet1!A:A` (only Name column)  
**✅ Correct**: `Sheet1!A:B` (Name + Email columns)  
**✅ Better**: `Sheet1!A:C` (Name + Email + Phone columns)

### **Fix 2: Check Column Headers**
Make sure your Google Sheet has proper headers:

**✅ Supported Email Headers:**
- `Email`
- `email` 
- `Email Address`
- `E-mail`
- `e-mail`
- `EMAIL`

### **Fix 3: Check Data Format**
Make sure emails are properly formatted:
- ✅ `<EMAIL>`
- ❌ `bilal` (missing @domain)
- ❌ Empty cells

---

## 🛠️ **Quick Test Sheet Setup:**

### **Create Test Google Sheet:**
1. Create new Google Sheet
2. Add headers in Row 1:
   ```
   A1: Name
   B1: Email
   ```
3. Add test data in Row 2:
   ```
   A2: Bilal Ahmad
   B2: <EMAIL>
   ```
4. Save the sheet

### **Test in Email Section:**
1. Go to Email section → Google Sheet
2. Select your test sheet
3. Set range: `Sheet1!A:B`
4. Click **Fetch Leads**
5. ✅ Should show: **Name: "Bilal Ahmad", Email: "<EMAIL>"**

---

## 🔍 **Troubleshooting by Debug Output:**

### **Scenario 1: Empty Raw Response**
```
📋 Raw response: undefined
```
**Fix**: Check Google Sheet permissions, make sure sheet exists

### **Scenario 2: Missing Email Column**
```
📝 Headers found: ["Name"]
📋 Sample row: ["Bilal Ahmad"]
```
**Fix**: Add Email column to your sheet or adjust range

### **Scenario 3: Wrong Range**
```
📝 Headers found: ["Name"]
📊 Data rows count: 0
```
**Fix**: Change range from `Sheet1!A:A` to `Sheet1!A:B`

### **Scenario 4: Empty Email Cells**
```
📋 Sample row: ["Bilal Ahmad", ""]
🔍 Sample lead object: {Name: "Bilal Ahmad", Email: ""}
```
**Fix**: Add email addresses to your Google Sheet

---

## 📧 **Email Sending Compatibility:**

The backend email sender supports these column names:
```javascript
const email =
  lead.email ||           // ✅ "email"
  lead.Email ||           // ✅ "Email" 
  lead.eMail ||           // ✅ "eMail"
  lead['Email Address'] || // ✅ "Email Address"
  lead['email'] ||        // ✅ "email" (with spaces)
  lead['EMAIL'] ||        // ✅ "EMAIL"
  lead['E-mail'] ||       // ✅ "E-mail"
  lead['e-mail'] ||       // ✅ "e-mail"
  Object.values(lead).find(val => typeof val === 'string' && val.includes('@'));
```

---

## ✅ **Action Plan:**

1. **Check your Google Sheet structure** - Make sure it has both Name and Email columns
2. **Verify the range** - Use `Sheet1!A:B` or similar to include both columns
3. **Test with debug logs** - Check browser console and backend terminal
4. **Fix based on debug output** - Use the troubleshooting guide above
5. **Test email sending** - After fixing, try sending emails to verify

---

## 🚀 **Expected Result:**

After fixing, you should see:
- ✅ **Recipients table** shows both Name AND Email
- ✅ **Debug logs** show proper lead objects with email fields
- ✅ **Email sending** works and sends to the extracted emails

**The issue is likely in your Google Sheet structure or range selection. Use the debug logs to identify the exact problem!** 📊🔍
