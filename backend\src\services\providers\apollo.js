import axios from "axios";

export const fetchLeadsFromApollo = async (prompt, maxResults) => {
  const options = {
    method: "POST",
    url: "https://apollo-io-leads-scraper.p.rapidapi.com/leads",
    headers: {
      "x-rapidapi-key": "**************************************************",
      "x-rapidapi-host": "apollo-io-leads-scraper.p.rapidapi.com",
      "Content-Type": "application/json",
    },
    data: {
      searchUrl: prompt,
    },
  };

  try {
    const response = await axios.request(options);
    return response.data;
  } catch (error) {
    console.error("Apollo API error:", error);
    return [];
  }
};