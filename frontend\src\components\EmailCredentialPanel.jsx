import { useState } from 'react';
import { X, Mail, Key, Server, Hash, AlertCircle } from 'lucide-react';

export default function EmailCredentialPanel({ onSubmit, onClear }) {
  const [credentials, setCredentials] = useState({
    email: '',
    password: '',
    host: 'smtp.gmail.com',
    port: '587'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // SMTP presets for common providers
  const smtpPresets = {
    gmail: { host: 'smtp.gmail.com', port: '587' },
    outlook: { host: 'smtp-mail.outlook.com', port: '587' },
    yahoo: { host: 'smtp.mail.yahoo.com', port: '587' },
    zoho: { host: 'smtp.zoho.com', port: '587' },
    custom: { host: '', port: '587' }
  };

  const detectProvider = (email) => {
    const domain = email.split('@')[1]?.toLowerCase();
    if (domain?.includes('gmail')) return 'gmail';
    if (domain?.includes('outlook') || domain?.includes('hotmail') || domain?.includes('live')) return 'outlook';
    if (domain?.includes('yahoo')) return 'yahoo';
    if (domain?.includes('zoho')) return 'zoho';
    return 'custom';
  };

  const handleEmailChange = (email) => {
    setCredentials(prev => ({ ...prev, email }));

    // Auto-detect and set SMTP settings
    const provider = detectProvider(email);
    if (provider !== 'custom') {
      setCredentials(prev => ({
        ...prev,
        email,
        host: smtpPresets[provider].host,
        port: smtpPresets[provider].port
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!credentials.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(credentials.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!credentials.password) {
      newErrors.password = 'App password is required';
    }

    if (!credentials.host) {
      newErrors.host = 'SMTP host is required';
    }

    if (!credentials.port || isNaN(credentials.port) || credentials.port < 1 || credentials.port > 65535) {
      newErrors.port = 'Please enter a valid port number (1-65535)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Test the credentials by attempting to create a connection
      const testResponse = await fetch(`${import.meta.env.VITE_API_SERVER_URL}/api/email/test-smtp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const result = await testResponse.json();

      if (result.success) {
        onSubmit(credentials);
      } else {
        setErrors({ general: result.message || 'Failed to connect with these credentials' });
      }
    } catch (error) {
      setErrors({ general: 'Failed to test SMTP connection. Please check your credentials.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setCredentials({ email: '', password: '', host: 'smtp.gmail.com', port: '587' });
    setErrors({});
    onClear();
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Mail className="w-5 h-5 mr-2 text-blue-600" />
          Email Credentials
        </h3>
        <button
          onClick={handleCancel}
          className="text-gray-400 hover:text-red-500 transition-colors"
          title="Cancel"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>📧 For Gmail users:</strong> Use an "App Password" instead of your regular password.
          <br />
          <a
            href="https://support.google.com/accounts/answer/185833"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 underline hover:text-blue-800"
          >
            Learn how to create an App Password →
          </a>
        </p>
      </div>

      {errors.general && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="w-4 h-4 text-red-600 mr-2 flex-shrink-0" />
          <p className="text-sm text-red-800">{errors.general}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <Mail className="w-4 h-4 inline mr-1" />
            Your Email Address
          </label>
          <input
            type="email"
            value={credentials.email}
            onChange={(e) => handleEmailChange(e.target.value)}
            placeholder="<EMAIL>"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <Key className="w-4 h-4 inline mr-1" />
            App Password / SMTP Key
          </label>
          <input
            type="password"
            value={credentials.password}
            onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
            placeholder="Enter your app password"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.password ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
          {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              <Server className="w-4 h-4 inline mr-1" />
              SMTP Host
            </label>
            <input
              type="text"
              value={credentials.host}
              onChange={(e) => setCredentials(prev => ({ ...prev, host: e.target.value }))}
              placeholder="smtp.gmail.com"
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.host ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isLoading}
            />
            {errors.host && <p className="text-red-500 text-xs mt-1">{errors.host}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              <Hash className="w-4 h-4 inline mr-1" />
              Port
            </label>
            <input
              type="number"
              value={credentials.port}
              onChange={(e) => setCredentials(prev => ({ ...prev, port: e.target.value }))}
              placeholder="587"
              min="1"
              max="65535"
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.port ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isLoading}
            />
            {errors.port && <p className="text-red-500 text-xs mt-1">{errors.port}</p>}
          </div>
        </div>

        <div className="pt-4">
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing Connection...
              </>
            ) : (
              'Save & Use These Credentials'
            )}
          </button>
        </div>
      </form>

      <div className="mt-4 text-xs text-gray-500">
        <p>🔒 Your credentials are only stored temporarily and never saved permanently.</p>
      </div>
    </div>
  );
}
