import React, { useState } from 'react';
import { XCircle } from 'lucide-react';

export default function EmailCredentialPanel({ onSubmit, onClear }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [host, setHost] = useState('smtp.gmail.com');
  const [port, setPort] = useState(587);

  const handleSubmit = () => {
    if (!email || !password || !host || !port) return alert("Please fill all fields");
    onSubmit({ email, password, host, port });
  };

  return (
    <div className="relative bg-white dark:bg-slate-800 shadow-md rounded-lg p-6 w-full max-w-md space-y-4">
      {/* ❌ Cancel icon */}
      <button
        onClick={onClear}
        className="absolute top-2 right-2 text-red-500 hover:text-red-700"
        title="Remove credentials"
      >
        <XCircle size={20} />
      </button>

      <h2 className="text-lg font-semibold text-gray-800 dark:text-white">🔐 SMTP Email Credentials</h2>

      <input
        type="email"
        placeholder="Your Email (e.g. <EMAIL>)"
        className="w-full p-2 rounded border border-gray-300"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />

      <input
        type="password"
        placeholder="App Password or SMTP Key"
        className="w-full p-2 rounded border border-gray-300"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />

      <input
        type="text"
        placeholder="SMTP Host (e.g. smtp.gmail.com)"
        className="w-full p-2 rounded border border-gray-300"
        value={host}
        onChange={(e) => setHost(e.target.value)}
      />

      <input
        type="number"
        placeholder="SMTP Port (e.g. 587)"
        className="w-full p-2 rounded border border-gray-300"
        value={port}
        onChange={(e) => setPort(Number(e.target.value))}
      />

      <button
        onClick={handleSubmit}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full"
      >
        Save & Use These Credentials
      </button>
    </div>
  );
}
