import nodemailer from 'nodemailer';

// Gmail transporter (default fallback)
export const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'kaat wqzu dldu qcbg'
  }
});

/**
 * Send email using either custom SMTP or Gmail.
 * If smtp config is provided, use it; otherwise, use Gmail transporter.
 * Supports attachments.
 */
export const sendEmail = async (params, to, subject, text, attachments = []) => {
  let mailTransporter;
  let from;

  // If params is an SMTP config object, use it
  if (params && params.host && params.email && params.password && params.port) {
    mailTransporter = nodemailer.createTransport({
      host: params.host,
      port: parseInt(params.port),
      secure: parseInt(params.port) === 465,
      auth: {
        user: params.email,
        pass: params.password
      }
    });
    from = params.email;
  } else {
    // Otherwise, use Gmail transporter
    mailTransporter = transporter;
    from = typeof params === 'string' ? params : transporter.options.auth.user;
  }

  const mailOptions = {
    from,
    to,
    subject,
    text
  };

  // Add attachments if provided
  if (attachments && attachments.length > 0) {
    mailOptions.attachments = attachments.map(attachment => ({
      filename: attachment.name,
      path: attachment.path
    }));
  }

  try {
    await mailTransporter.sendMail(mailOptions);
    console.log(`✅ Email sent successfully to ${to}${attachments?.length ? ` with ${attachments.length} attachment(s)` : ''}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to send email to ${to}: ${error.message}`);
    return false;
  }
};
