import nodemailer from 'nodemailer';

export const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'kaat wqzu dldu qcbg'
  }
});

export const sendEmail = async (from, to, subject, text) => {
  try {
    await transporter.sendMail({
      from,
      to,
      subject,
      text
    });
    return true;
  } catch (error) {
    console.error(`❌ Failed to send email to ${to}: ${error.message}`);
    return false;
  }
};
