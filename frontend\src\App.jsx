import React, { useState, useRef, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import Chat from './components/Chat';
import ExportModal from './components/ExportModal.jsx';
import LeadsTable from './components/LeadsTable.jsx';
import EmailSection from './components/EmailSender';

function App() {
  const [conversations, setConversations] = useState([]);
  const [activeConversationId, setActiveConversationId] = useState(null);
  const [sidebarWidth, setSidebarWidth] = useState(320);
  const [isResizing, setIsResizing] = useState(false);
  const [leadsData, setLeadsData] = useState([]);
  const [emailLeads, setEmailLeads] = useState([]);
  const [isExportModalOpen, setExportModalOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState('scraper');
  const [screen, setScreen] = useState('chat');
  const sidebarRef = useRef(null);

  // 🧹 Function to clear backend uploaded files
  const clearBackendFiles = async () => {
    try {
      const apiUrl = import.meta.env.VITE_API_SERVER_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/email/clear-uploads`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        console.log('🗑️ Backend files cleared successfully');
      } else {
        console.log('⚠️ Backend file cleanup endpoint not available');
      }
    } catch (error) {
      console.log('⚠️ Could not clear backend files:', error.message);
    }
  };

  const activeConversation = conversations.find(c => c.id === activeConversationId);

  // 🧹 AUTO-CLEANUP: Clear all data on app load/refresh
  useEffect(() => {
    console.log('🧹 Auto-cleanup: Clearing all leads, files, and attachments on app refresh');

    // Clear all state data
    setLeadsData([]);
    setEmailLeads([]);
    setExportModalOpen(false);

    // Clear localStorage data related to leads and files
    const keysToRemove = [
      'googleAuthData',
      'uploadedFiles',
      'emailAttachments',
      'scrapedLeads',
      'exportData'
    ];

    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        console.log(`🗑️ Cleared localStorage: ${key}`);
      }
    });

    // Clear conversations with leads data
    const stored = localStorage.getItem('conversations');
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        // Remove leads from all conversations but keep conversation history
        const cleanedConversations = parsed.map(conv => ({
          ...conv,
          messages: conv.messages.map(msg => ({
            ...msg,
            leads: undefined // Remove leads data
          }))
        }));

        setConversations(cleanedConversations);
        setActiveConversationId(cleanedConversations[0]?.id || null);
        localStorage.setItem('conversations', JSON.stringify(cleanedConversations));

        console.log('🧹 Cleaned conversation leads data');
      } catch (error) {
        console.error('Error cleaning conversations:', error);
        createNewConversation();
      }
    } else {
      createNewConversation();
    }

    // Clear any uploaded files from backend (if endpoint exists)
    clearBackendFiles();

    console.log('✅ Auto-cleanup completed - App reset to clean state');
  }, []); // Empty dependency array = runs only on mount/refresh

  // Save conversations but exclude leads data to prevent persistence
  useEffect(() => {
    const conversationsToSave = conversations.map(conv => ({
      ...conv,
      messages: conv.messages.map(msg => ({
        ...msg,
        leads: undefined // Don't persist leads data
      }))
    }));
    localStorage.setItem('conversations', JSON.stringify(conversationsToSave));
  }, [conversations]);

  const updateConversation = (conversationId, updates) => {
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, ...updates, updatedAt: new Date() }
          : conv
      )
    );
  };

  const addMessage = (conversationId, message) => {
    const newMessage = {
      ...message,
      id: Date.now().toString(),
    };

    updateConversation(conversationId, {
      messages: [
        ...(conversations.find(c => c.id === conversationId)?.messages || []),
        newMessage
      ]
    });

    return newMessage;
  };

  const createNewConversation = () => {
    const newConv = {
      id: Date.now().toString(),
      title: 'New Conversation',
      messages: [
        {
          id: '1',
          type: 'bot',
          content: 'Hello! I\'m your lead generation assistant. How can I help you generate leads today?',
          timestamp: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setConversations(prev => [newConv, ...prev]);
    setActiveConversationId(newConv.id);
  };

  const deleteConversation = (conversationId) => {
    const updated = conversations.filter(c => c.id !== conversationId);
    setConversations(updated);
    if (activeConversationId === conversationId) {
      setActiveConversationId(updated[0]?.id || null);
    }
  };

  const updateConversationTitle = (conversationId, title) => {
    updateConversation(conversationId, { title });
  };

  const handleMouseDown = (e) => {
    setIsResizing(true);
    e.preventDefault();
  };

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    const newWidth = Math.max(250, Math.min(500, e.clientX));
    setSidebarWidth(newWidth);
  };

  const handleMouseUp = () => {
    setIsResizing(false);
  };

  useEffect(() => {
    const handleGoogleAuthSuccess = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const authDataParam = urlParams.get('authData');

      if (window.location.pathname === '/google-auth-success' && authDataParam) {
        try {
          const authData = JSON.parse(decodeURIComponent(authDataParam));
          localStorage.setItem('googleAuthData', JSON.stringify(authData));

          if (window.opener) {
            window.opener.postMessage({
              type: 'GOOGLE_AUTH_SUCCESS',
              authData: authData
            }, window.location.origin);
            window.close();
          } else {
            sessionStorage.setItem('googleAuthData', JSON.stringify(authData));
            window.location.href = '/';
          }
        } catch (error) {
          console.error('Failed to parse auth data:', error);
        }
      }

      const storedAuthData = sessionStorage.getItem('googleAuthData');
      if (storedAuthData) {
        sessionStorage.removeItem('googleAuthData');
        try {
          const authData = JSON.parse(storedAuthData);
          window.dispatchEvent(new CustomEvent('googleAuthComplete', { detail: { authData } }));
        } catch (error) {
          console.error('Failed to parse stored auth data:', error);
        }
      }
    };

    handleGoogleAuthSuccess();
  }, []);

  return (
    <div
      className="flex h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 overflow-hidden"
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      <div
        ref={sidebarRef}
        className="relative flex-shrink-0 bg-white/10 backdrop-blur-lg border-r border-white/20 h-full"
        style={{ width: sidebarWidth, minWidth: sidebarWidth, maxWidth: sidebarWidth }}
      >
        <Sidebar
          conversations={conversations}
          activeConversationId={activeConversationId}
          onConversationSelect={setActiveConversationId}
          onNewConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onUpdateTitle={updateConversationTitle}
          onChangeScreen={setScreen}
        />
        <div
          className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-400/50 transition-colors"
          onMouseDown={handleMouseDown}
        />
      </div>

      <div className="flex-1 flex flex-col min-w-0 h-full overflow-hidden">
        {/* Top Bar: only show on 'lead' screen */}
        {screen === 'chat' && (
          <div className="flex justify-end items-center px-6 py-4 bg-gradient-to-r from-slate-900 to-blue-900 relative z-20">
            <div>
              <label className="mr-2 font-semibold text-white">Lead Source:</label>
              <select
                value={selectedSource}
                onChange={e => setSelectedSource(e.target.value)}
                className="border border-gray-300 rounded px-2 py-1 text-black"
                style={{ minWidth: 120 }}
              >
                <option value="apify">Apify</option>
                <option value="apollo">Apollo</option>
                <option value="scraper">Scraper</option>
              </select>
            </div>
          </div>
        )}

        {screen === 'chat' && activeConversation && (
          <Chat
            selectedSource={selectedSource}
            conversation={activeConversation}
            onSendMessage={(content) => addMessage(activeConversation.id, {
              type: 'user',
              content,
              timestamp: new Date()
            })}
            onBotResponse={(content, leads) => {
              setLeadsData(leads || []);
              addMessage(activeConversation.id, {
                type: 'bot',
                content,
                timestamp: new Date(),
                leads
              });
            }}
            onSendEmails={(leads) => {
              setEmailLeads(leads);
              setScreen('email');
            }}
          />
        )}

        {screen === 'lead' && <LeadsTable />}
        {screen === 'email' && <EmailSection scrapedLeads={emailLeads} goBack={() => setScreen('chat')} />}
      </div>

      <ExportModal
        leads={leadsData}
        isOpen={isExportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />
    </div>
  );
}

export default App;
