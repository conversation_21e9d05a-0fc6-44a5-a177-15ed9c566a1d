import express from 'express';
import multer from 'multer';
import { sendEmail } from '../emailSender.js';

const router = express.Router();

const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => cb(null, Date.now() + '-' + file.originalname)
});
const upload = multer({ storage });

router.post('/upload', upload.single('file'), (req, res) => {
  res.json({ path: req.file.path, name: req.file.originalname });
});

router.post('/send-emails', async (req, res) => {
  try {
    const { from, leads, subject, description, attachments } = req.body;

    console.log(`📧 Email request received:`);
    console.log(`   📊 Leads count: ${leads?.length || 0}`);
    console.log(`   📎 Attachments: ${attachments?.length || 0}`, attachments);
    console.log(`   📧 From: ${from}`);
    console.log(`   📝 Subject: ${subject}`);

    if (!Array.isArray(leads) || leads.length === 0) {
      return res.status(400).json({ message: "No leads provided." });
    }

    if (!from || !subject || !description) {
      return res.status(400).json({ message: "Missing required fields: from, subject, or description." });
    }

    let emailsSent = 0;
    let emailsSkipped = 0;

    for (const lead of leads) {
    // Auto-detect email - Enhanced for Google Sheets
    const email =
      lead.email ||
      lead.Email ||
      lead.eMail ||
      lead['Email Address'] ||
      lead['email'] ||
      lead['EMAIL'] ||
      lead['E-mail'] ||
      lead['e-mail'] ||
      Object.values(lead).find(val => typeof val === 'string' && val.includes('@'));

    // Auto-detect name - Enhanced for Google Sheets (moved before email check for logging)
    const firstName = lead.firstName || lead.FirstName || lead['First Name'] || '';
    const lastName = lead.lastName || lead.LastName || lead['Last Name'] || '';
    const fullName = lead.name || lead.Name || lead['Name'] || '';

    const name = fullName || `${firstName} ${lastName}`.trim() || 'Valued Customer';

    if (!email) {
      console.warn("❌ Skipping lead with no email:", lead);
      console.warn("   📋 Available keys:", Object.keys(lead));
      console.warn("   👤 Lead name:", name);
      emailsSkipped++;
      continue;
    }

    console.log(`📧 Processing lead: ${name} <${email}>`);

    // Personalized message
    const personalizedMessage = name
      ? `Hi ${name},\n\n${description}`
      : description;

    // Send email with attachments
    const emailResult = await sendEmail(from, email, subject, personalizedMessage, attachments);
    if (emailResult) {
      emailsSent++;
    } else {
      emailsSkipped++;
    }
    await new Promise(res => setTimeout(res, 4000));
  }

  console.log(`📊 Email sending completed: ${emailsSent} sent, ${emailsSkipped} skipped`);
  res.json({
    message: 'Emails sent successfully',
    emailsSent,
    emailsSkipped,
    totalLeads: leads.length
  });

  } catch (error) {
    console.error('❌ Email sending error:', error);
    res.status(500).json({
      message: 'Failed to send emails',
      error: error.message
    });
  }
});

// 🧹 COMPREHENSIVE AUTO-CLEANUP ENDPOINT
router.post('/clear-uploads', async (req, res) => {
  try {
    const fs = await import('fs');
    const path = await import('path');

    const uploadsDir = 'uploads/';
    let deletedCount = 0;
    let errors = [];

    console.log('🧹 Starting comprehensive cleanup...');

    // Clear uploaded files
    if (fs.existsSync(uploadsDir)) {
      try {
        const files = fs.readdirSync(uploadsDir);

        for (const file of files) {
          const filePath = path.join(uploadsDir, file);
          try {
            const stats = fs.statSync(filePath);
            if (stats.isFile()) {
              fs.unlinkSync(filePath);
              deletedCount++;
              console.log(`🗑️ Deleted file: ${file}`);
            }
          } catch (deleteError) {
            errors.push(`Could not delete ${file}: ${deleteError.message}`);
            console.warn(`⚠️ Could not delete ${file}:`, deleteError.message);
          }
        }
      } catch (readError) {
        errors.push(`Could not read uploads directory: ${readError.message}`);
      }
    } else {
      console.log('📁 Uploads directory does not exist, creating it...');
      try {
        fs.mkdirSync(uploadsDir, { recursive: true });
        console.log('✅ Created uploads directory');
      } catch (createError) {
        errors.push(`Could not create uploads directory: ${createError.message}`);
      }
    }

    // Clear any in-memory caches or temporary data
    // (Add any global variables or caches your app uses)
    console.log('🧹 Clearing in-memory data...');

    const result = {
      success: true,
      message: `Cleanup completed: ${deletedCount} files deleted`,
      deletedCount,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ Cleanup completed:`, result);
    res.json(result);

  } catch (error) {
    console.error('❌ Critical error during cleanup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform cleanup',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 🧹 CLEANUP ON STARTUP - Clear old files when server starts
router.get('/startup-cleanup', async (req, res) => {
  try {
    console.log('🧹 Performing startup cleanup...');

    const fs = await import('fs');
    const path = await import('path');
    const uploadsDir = 'uploads/';

    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      const now = Date.now();
      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(uploadsDir, file);
        try {
          const stats = fs.statSync(filePath);
          const fileAge = now - stats.mtime.getTime();

          // Delete files older than 1 hour
          if (fileAge > 3600000) {
            fs.unlinkSync(filePath);
            cleanedCount++;
            console.log(`🗑️ Cleaned old file: ${file}`);
          }
        } catch (error) {
          console.warn(`⚠️ Could not process ${file}:`, error.message);
        }
      }

      console.log(`✅ Startup cleanup: ${cleanedCount} old files removed`);
    }

    res.json({ success: true, message: 'Startup cleanup completed' });
  } catch (error) {
    console.error('❌ Startup cleanup error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

export default router;
