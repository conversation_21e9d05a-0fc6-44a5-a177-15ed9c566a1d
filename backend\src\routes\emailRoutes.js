import express from 'express';
import multer from 'multer';
import { sendEmail } from '../emailSender.js';

const router = express.Router();

const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => cb(null, Date.now() + '-' + file.originalname)
});
const upload = multer({ storage });

router.post('/upload', upload.single('file'), (req, res) => {
  res.json({ path: req.file.path, name: req.file.originalname });
});

router.post('/send-emails', async (req, res) => {
  const { from, leads, subject, description } = req.body;

  if (!Array.isArray(leads) || leads.length === 0) {
    return res.status(400).json({ message: "No leads provided." });
  }

  for (const lead of leads) {
    // Auto-detect email
    const email =
      lead.email ||
      lead.Email ||
      lead.eMail ||
      lead['Email Address'] ||
      Object.values(lead).find(val => typeof val === 'string' && val.includes('@'));

    if (!email) {
      console.warn("❌ Skipping lead with no email:", lead);
      continue;
    }

    // Auto-detect name (can be improved later to handle fullName too)
    const name = `${lead.firstName || lead.FirstName || ''} ${lead.lastName || lead.LastName || ''}`.trim();

    // Personalized message
    const personalizedMessage = name
      ? `Hi ${name},\n\n${description}`
      : description;

    // Send email
    await sendEmail(from, email, subject, personalizedMessage);
    await new Promise(res => setTimeout(res, 4000));
  }

  res.json({ message: 'Emails sent successfully' });
});

export default router;
