import express from 'express';
import multer from 'multer';
import nodemailer from 'nodemailer';

const router = express.Router();

// Helper function to create SMTP transporter
const createTransporter = (smtpConfig) => {
  return nodemailer.createTransport({
    host: smtpConfig.host,
    port: parseInt(smtpConfig.port),
    secure: parseInt(smtpConfig.port) === 465, // true for 465, false for other ports
    auth: {
      user: smtpConfig.email,
      pass: smtpConfig.password,
    },
    tls: {
      rejectUnauthorized: false // Allow self-signed certificates
    }
  });
};

const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => cb(null, Date.now() + '-' + file.originalname)
});
const upload = multer({ storage });

// Test SMTP connection endpoint
router.post('/test-smtp', async (req, res) => {
  try {
    const { email, password, host, port } = req.body;

    console.log(`🧪 Testing SMTP connection for ${email} on ${host}:${port}`);

    if (!email || !password || !host || !port) {
      return res.status(400).json({
        success: false,
        message: "Missing SMTP credentials"
      });
    }

    const transporter = createTransporter({ email, password, host, port });

    // Verify the connection
    await transporter.verify();

    console.log(`✅ SMTP connection successful for ${email}`);
    res.json({
      success: true,
      message: "SMTP connection successful"
    });

  } catch (error) {
    console.error(`❌ SMTP connection failed:`, error.message);
    res.status(400).json({
      success: false,
      message: `Connection failed: ${error.message}`
    });
  }
});

router.post('/upload', upload.single('file'), (req, res) => {
  res.json({ path: req.file.path, name: req.file.originalname });
});

// Send emails using SMTP credentials
router.post('/send-emails', async (req, res) => {
  try {
    const { smtp, leads, subject, description, attachments } = req.body;

    console.log(`📧 SMTP Email send request received:`);
    console.log(`   📊 Leads count: ${leads?.length || 0}`);
    console.log(`   📧 From: ${smtp?.email}`);
    console.log(`   📝 Subject: ${subject}`);

    if (!smtp || !smtp.email || !smtp.password || !smtp.host || !smtp.port) {
      return res.status(400).json({ message: "Missing SMTP credentials" });
    }

    if (!Array.isArray(leads) || leads.length === 0) {
      return res.status(400).json({ message: "No leads provided." });
    }

    if (!subject || !description) {
      return res.status(400).json({ message: "Missing required fields: subject or description." });
    }

    let emailsSent = 0;
    let emailsSkipped = 0;
    const transporter = createTransporter(smtp);

    for (const lead of leads) {
      // Auto-detect email - Enhanced for Google Sheets
      const email =
        lead.email ||
        lead.Email ||
        lead.eMail ||
        lead['Email Address'] ||
        lead['email'] ||
        lead['EMAIL'] ||
        lead['E-mail'] ||
        lead['e-mail'] ||
        Object.values(lead).find(val => typeof val === 'string' && val.includes('@'));

      // Auto-detect name - Enhanced for Google Sheets
      const firstName = lead.firstName || lead.FirstName || lead['First Name'] || '';
      const lastName = lead.lastName || lead.LastName || lead['Last Name'] || '';
      const fullName = lead.name || lead.Name || lead['Name'] || '';
      const name = fullName || `${firstName} ${lastName}`.trim() || 'Valued Customer';

      if (!email) {
        console.warn("❌ Skipping lead with no email:", lead);
        console.warn("   📋 Available keys:", Object.keys(lead));
        console.warn("   👤 Lead name:", name);
        emailsSkipped++;
        continue;
      }

      console.log(`📧 Processing lead: ${name} <${email}>`);

      try {
        // Personalized message
        const personalizedMessage = name !== 'Valued Customer'
          ? `Hi ${name},\n\n${description}`
          : description;

        // Prepare mail options
        const mailOptions = {
          from: `"${smtp.email}" <${smtp.email}>`,
          to: email,
          subject: subject,
          text: personalizedMessage,
          html: personalizedMessage.replace(/\n/g, '<br>'),
          attachments: attachments || []
        };

        // Send email
        await transporter.sendMail(mailOptions);
        emailsSent++;
        console.log(`✅ Email sent to ${email}`);

        // Add delay between emails to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (err) {
        console.error(`❌ Failed to send to ${email}: ${err.message}`);
        emailsSkipped++;
      }
    }

    console.log(`📊 Email sending completed: ${emailsSent} sent, ${emailsSkipped} skipped`);
    res.json({
      message: 'Emails sent successfully',
      emailsSent,
      emailsSkipped,
      totalLeads: leads.length
    });

  } catch (error) {
    console.error('❌ Email sending error:', error);
    res.status(500).json({
      message: 'Failed to send emails',
      error: error.message
    });
  }
});

export default router;
